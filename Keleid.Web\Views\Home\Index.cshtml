@model Keleid.Web.ViewModels.HomeIndexViewModel
@{
    ViewData["Title"] = "صفحه اصلی";
}

<!-- Main Content -->
<div class="container">
    <!-- Mobile Categories -->
    <div class="mobile-categories d-grid d-lg-none">
        @{
            // تشخیص دسته‌بندی اصلی انتخاب شده
            var selectedMainCategory = Model?.SelectedCategory != null && Model.SelectedCategory.ParentCategoryId == null
                ? Model.Categories.FirstOrDefault(c => c.Id == Model.SelectedCategory.Id)
                : Model?.SelectedCategory != null && Model.SelectedCategory.ParentCategoryId != null
                    ? Model.Categories.FirstOrDefault(c => c.Id == Model.SelectedCategory.ParentCategoryId)
                    : null;
        }

        @if (selectedMainCategory != null && selectedMainCategory.SubCategories.Any())
        {
            <!-- دکمه بازگشت -->
            <div class="mobile-back-button">
                <a href="/" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-arrow-right me-1"></i>
                    بازگشت به دسته‌بندی‌ها
                </a>
            </div>

            <!-- نمایش زیردسته‌ها -->
            @if (!string.IsNullOrEmpty(selectedMainCategory.Slug))
            {
                var allItemsActiveClass = !string.IsNullOrEmpty(Model.SelectedSlug) && selectedMainCategory.Slug == Model.SelectedSlug ? "active" : "";
                <a asp-action="Index" asp-route-slug="@selectedMainCategory.Slug" class="category-item @allItemsActiveClass">
                    <span class="category-name">همه موارد</span>
                </a>
            }
            @foreach (var subCategory in selectedMainCategory.SubCategories)
            {
                var subCategoryActiveClass = !string.IsNullOrEmpty(Model.SelectedSlug) && subCategory.Slug == Model.SelectedSlug ? "active" : "";
                <a asp-action="Index" asp-route-slug="@subCategory.Slug" class="category-item @subCategoryActiveClass">
                    <span class="category-name">@subCategory.Title</span>
                </a>
            }
        }
        else
        {
            <!-- نمایش دسته‌بندی‌های اصلی -->
            @if (Model?.Categories != null && Model.Categories.Any())
            {
                @for (int i = 0; i < Model.Categories.Count; i++)
                {
                    var category = Model.Categories[i];
                    var bgClass = $"bg-cat-{(i % 4) + 1}"; // چرخش بین bg-cat-1 تا bg-cat-4
                    <a asp-action="Index" asp-route-slug="@category.Slug" class="category-item" data-category-id="@category.Id">
                        <div class="category-icon @bgClass">
                            <i class="@category.IconClass"></i>
                        </div>
                        <span class="category-name">@category.Title</span>
                    </a>
                }
            }
        }
    </div>

    <div class="row">
        <!-- Desktop Sidebar -->
        <div class="col-md-3 d-none d-lg-block">
            <div class="categories-sidebar">
                <h6 class="mb-3">دسته‌ها</h6>
                <ul class="list-unstyled">
                    <!-- گزینه همه دسته‌بندی‌ها -->
                    <li class="category-item-wrapper">
                        <div class="d-flex justify-content-between align-items-center">
                            @{
                                var allCategoriesActiveClass = string.IsNullOrEmpty(Model.SelectedSlug) ? "active" : "";
                            }
                            <a href="/" class="category-title @allCategoriesActiveClass text-decoration-none">
                                <i class="fas fa-th-large"></i> همه دسته‌بندی‌ها
                            </a>
                        </div>
                    </li>

                    @if (Model?.Categories != null && Model.Categories.Any())
                    {
                        @foreach (var category in Model.Categories)
                        {
                            var isParentOfSelected = !string.IsNullOrEmpty(Model.SelectedSlug) &&
                                                   (category.SubCategories.Any(sc => sc.Slug == Model.SelectedSlug) ||
                                                    (!string.IsNullOrEmpty(category.Slug) && category.Slug == Model.SelectedSlug));
                            var shouldBeOpen = isParentOfSelected ? "show" : "";
                            var categoryActiveClass = Model.SelectedCategory != null && Model.SelectedCategory.ParentCategoryId == null &&
                                                    Model.SelectedCategory.Id == category.Id ? "active" : "";

                            <li class="category-item-wrapper">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="category-title @categoryActiveClass" data-category-id="@category.Id">
                                        <i class="@category.IconClass"></i> @category.Title
                                    </span>
                                    @if (category.SubCategories.Any())
                                    {
                                        <button class="btn btn-link dropdown-toggle p-1" data-bs-toggle="collapse" data-bs-target="#category@(category.Id)">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    }
                                </div>
                                @if (category.SubCategories.Any())
                                {
                                    <div class="collapse subcategories @shouldBeOpen" id="category@(category.Id)">
                                        <ul class="list-unstyled pr-3 mt-2">
                                            @if (!string.IsNullOrEmpty(category.Slug))
                                            {
                                                var allItemsActiveClass = !string.IsNullOrEmpty(Model.SelectedSlug) && category.Slug == Model.SelectedSlug ? "active" : "";
                                                <li><a asp-action="Index" asp-route-slug="@category.Slug" class="subcategory-link @allItemsActiveClass">همه موارد</a></li>
                                            }
                                            @foreach (var subCategory in category.SubCategories)
                                            {
                                                var subCategoryActiveClass = !string.IsNullOrEmpty(Model.SelectedSlug) && subCategory.Slug == Model.SelectedSlug ? "active" : "";
                                                <li><a asp-action="Index" asp-route-slug="@subCategory.Slug" class="subcategory-link @subCategoryActiveClass" data-subcategory-id="@subCategory.Id">@subCategory.Title</a></li>
                                            }
                                        </ul>
                                    </div>
                                }
                            </li>
                        }
                    }
                </ul>
                <hr class="my-4">
                <div class="sidebar-footer">
                    <div class="footer-links mb-4">
                        <a asp-controller="Info" asp-action="About" class="text-muted me-2">درباره کلید</a>
                        <a asp-controller="Info" asp-action="Download" class="text-muted me-2">
                            دانلود اپلیکیشن
                        </a>
                        <a asp-controller="Info" asp-action="Terms" class="text-muted me-2">قوانین و مقررات</a>
                        <a asp-controller="Info" asp-action="Contact" class="text-muted me-2">ارتباط با ما</a>
                    </div>
                    <div class="social-links mb-4">
                        <a href="#" class="text-muted me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-muted me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-muted me-3"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-muted"><i class="fas fa-th"></i></a>
                    </div>
                    <div class="trust-logos">
                        <img src="/assets/img/enamad.png" alt="نماد اعتماد الکترونیکی" height="60" class="me-2">
                        <img src="/assets/img/samandehi.png" alt="نماد ساماندهی" height="60" class="me-2">
                        <img src="/assets/img/etehadieh.png" alt="مجوز کسب و کار" height="60">
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            @if (!string.IsNullOrEmpty(Model.SearchTerm))
            {
                <div class="d-flex align-items-center justify-content-between mb-3">
                    <h6 class="section-title mb-0">نتایج جستجو برای: "@Model.SearchTerm"</h6>
                    <a href="/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-1"></i>
                        پاک کردن فیلتر
                    </a>
                </div>
            }
            else
            {
                <h6 class="section-title d-lg-none">انواع آگهی‌ها و نیازمندی‌های @(ViewBag.SelectedProvince ?? "کل ایران")</h6>
            }
            <div class="row g-3" id="advertisements-container">
                @if (Model?.LatestAdvertisements != null && Model.LatestAdvertisements.Any())
                {
                    @foreach (var ad in Model.LatestAdvertisements)
                    {
                        <div class="col-md-6 col-lg-4">
                            <a href="/@ad.Id/@ad.Slug" class="text-decoration-none">
                                <div class="ad-card">
                                    <div class="card-content">
                                        <div class="card-img-wrapper">
                                            <img src="@ad.MainImageUrl" class="card-img-top" alt="@ad.Title">
                                        </div>
                                        <div class="card-body">
                                            <h5 class="card-title">@ad.Title</h5>
                                            <p class="price mb-2">@ad.FormattedPrice</p>
                                            <p class="location">
                                                <i class="fas fa-location-dot ms-1"></i>
                                                @ad.LocationDisplay
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    }
                }
                else
                {
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            @if (!string.IsNullOrEmpty(Model.SearchTerm))
                            {
                                <h5 class="text-muted">نتیجه‌ای برای "@Model.SearchTerm" یافت نشد</h5>
                                <p class="text-muted">لطفاً با کلمات کلیدی دیگری جستجو کنید.</p>
                            }
                            else
                            {
                                <h5 class="text-muted">هیچ آگهی یافت نشد</h5>
                                <p class="text-muted">در حال حاضر آگهی‌ای برای نمایش وجود ندارد.</p>
                            }
                        </div>
                    </div>
                }
            </div>

            <!-- Loading Indicator -->
            <div class="text-center mt-4" id="loading-indicator" style="display: none;">
                <div class="spinner-border text-danger" role="status">
                    <span class="visually-hidden">در حال بارگذاری...</span>
                </div>
                <p class="mt-2 text-muted">در حال بارگذاری آگهی‌های بیشتر...</p>
            </div>

            <!-- End of Results Indicator -->
            <div class="text-center mt-4" id="end-of-results" style="display: none;">
                <p class="text-muted">همه آگهی‌ها نمایش داده شدند</p>
            </div>
        </div>
    </div>
</div>

@section js {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get all category titles
            const categoryTitles = document.querySelectorAll('.category-title');

            // Add click event listener to each title
            categoryTitles.forEach(title => {
                title.addEventListener('click', function() {
                    // Find the parent category-item-wrapper
                    const wrapper = this.closest('.category-item-wrapper');
                    if (wrapper) {
                        // Find the collapse element and toggle button within this wrapper
                        const collapseElement = wrapper.querySelector('.collapse');
                        const toggleButton = wrapper.querySelector('.dropdown-toggle');

                        if (collapseElement && toggleButton) {
                            // Click the toggle button to trigger the collapse
                            toggleButton.click();
                        }
                    }
                });
            });

            // Infinite Scroll Implementation
            let currentPage = @Model.CurrentPage;
            let hasMoreAds = @Model.HasMoreAds.ToString().ToLower();
            let isLoading = false;
            const slug = '@Html.Raw(Model.SelectedSlug ?? "")';
            const searchTerm = '@Html.Raw(Model.SearchTerm ?? "")';

            function loadMoreAds() {
                if (isLoading || !hasMoreAds) return;

                isLoading = true;
                document.getElementById('loading-indicator').style.display = 'block';

                const formData = new FormData();
                formData.append('page', currentPage + 1);
                if (slug && slug.trim() !== '') formData.append('slug', slug);
                if (searchTerm && searchTerm.trim() !== '') formData.append('search', searchTerm);

                fetch('/LoadMoreAds', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Response data:', data);
                    if (data.success && data.advertisements && data.advertisements.length > 0) {
                        const container = document.getElementById('advertisements-container');

                        data.advertisements.forEach(ad => {
                            const adHtml = `
                                <div class="col-md-6 col-lg-4">
                                    <a href="/${ad.id}/${ad.slug}" class="text-decoration-none">
                                        <div class="ad-card">
                                            <div class="card-content">
                                                <div class="card-img-wrapper">
                                                    <img src="${ad.mainImageUrl}" class="card-img-top" alt="${ad.title}">
                                                </div>
                                                <div class="card-body">
                                                    <h5 class="card-title">${ad.title}</h5>
                                                    <p class="price mb-2">${ad.formattedPrice}</p>
                                                    <p class="location">
                                                        <i class="fas fa-location-dot ms-1"></i>
                                                        ${ad.locationDisplay}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            `;
                            container.insertAdjacentHTML('beforeend', adHtml);
                        });

                        currentPage++;
                        hasMoreAds = data.hasMore;

                        if (!hasMoreAds) {
                            document.getElementById('end-of-results').style.display = 'block';
                        }
                    } else {
                        console.log('No more ads or request failed');
                        if (!data.success) {
                            console.error('Server error:', data.message);
                        }
                        if (!data.advertisements || data.advertisements.length === 0) {
                            document.getElementById('end-of-results').style.display = 'block';
                            hasMoreAds = false;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading more ads:', error);
                })
                .finally(() => {
                    isLoading = false;
                    document.getElementById('loading-indicator').style.display = 'none';
                });
            }

            // Scroll event listener
            window.addEventListener('scroll', function() {
                if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight - 1000) {
                    loadMoreAds();
                }
            });
        });
    </script>
}