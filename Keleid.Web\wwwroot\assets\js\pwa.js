// PWA Installation and Service Worker Registration
// Use global variable to avoid conflicts
window.deferredPrompt = null;
let installButton;

// Register Service Worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// Handle PWA install prompt
window.addEventListener('beforeinstallprompt', (e) => {
  // Prevent the mini-infobar from appearing on mobile
  e.preventDefault();
  // Stash the event so it can be triggered later
  window.deferredPrompt = e;
  // Show install button
  showInstallButton();
});

// Check if device is mobile
function isMobileDevice() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
         window.innerWidth <= 768;
}

// Show install button
function showInstallButton() {
  // Only show floating button on mobile devices
  if (!isMobileDevice()) {
    return;
  }

  // Create install button if it doesn't exist
  if (!document.getElementById('pwa-install-btn')) {
    const installBtn = document.createElement('button');
    installBtn.id = 'pwa-install-btn';
    installBtn.className = 'btn btn-outline-danger btn-sm position-fixed';
    installBtn.style.cssText = `
      bottom: 80px;
      left: 20px;
      z-index: 1050;
      border-radius: 25px;
      padding: 8px 16px;
      font-size: 12px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      display: none;
    `;
    installBtn.innerHTML = '<i class="fas fa-download me-1"></i> نصب اپلیکیشن';
    installBtn.addEventListener('click', goToDownloadPage);
    document.body.appendChild(installBtn);
  }

  // Show the button with animation
  const btn = document.getElementById('pwa-install-btn');
  if (btn && window.deferredPrompt) {
    btn.style.display = 'block';
    setTimeout(() => {
      btn.style.opacity = '1';
      btn.style.transform = 'translateY(0)';
    }, 100);
  }
}

// Go to download page instead of direct install
function goToDownloadPage() {
  window.location.href = '/Info/Download';
}

// Install PWA (kept for download page usage)
async function installPWA() {
  if (!window.deferredPrompt) return;

  // Show the install prompt
  window.deferredPrompt.prompt();

  // Wait for the user to respond to the prompt
  const { outcome } = await window.deferredPrompt.userChoice;

  if (outcome === 'accepted') {
    console.log('User accepted the install prompt');
    hideInstallButton();
  } else {
    console.log('User dismissed the install prompt');
  }

  // Clear the deferredPrompt
  window.deferredPrompt = null;
}

// Hide install button
function hideInstallButton() {
  const btn = document.getElementById('pwa-install-btn');
  if (btn) {
    btn.style.opacity = '0';
    btn.style.transform = 'translateY(20px)';
    setTimeout(() => {
      btn.style.display = 'none';
    }, 300);
  }
}

// Handle app installed event
window.addEventListener('appinstalled', () => {
  console.log('PWA was installed');
  hideInstallButton();
  
  // Show success message
  if (typeof bootstrap !== 'undefined') {
    const toast = document.createElement('div');
    toast.className = 'toast position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060;';
    toast.innerHTML = `
      <div class="toast-header">
        <i class="fas fa-check-circle text-success me-2"></i>
        <strong class="me-auto">کلید</strong>
        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
      </div>
      <div class="toast-body">
        اپلیکیشن با موفقیت نصب شد!
      </div>
    `;
    document.body.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
      document.body.removeChild(toast);
    });
  }
});

// Check if app is running in standalone mode
function isStandalone() {
  return window.matchMedia('(display-mode: standalone)').matches || 
         window.navigator.standalone === true;
}

// Hide install button if already installed
if (isStandalone()) {
  hideInstallButton();
}

// iOS Safari specific handling
function isIOS() {
  return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
}

function isInStandaloneMode() {
  return ('standalone' in window.navigator) && (window.navigator.standalone);
}

// Show iOS install instructions
function showIOSInstallInstructions() {
  if (isIOS() && !isInStandaloneMode() && isMobileDevice()) {
    // Create iOS install prompt
    const iosPrompt = document.createElement('div');
    iosPrompt.id = 'ios-install-prompt';
    iosPrompt.className = 'alert alert-info position-fixed';
    iosPrompt.style.cssText = `
      bottom: 80px;
      left: 20px;
      right: 20px;
      z-index: 1050;
      border-radius: 10px;
      display: none;
      font-size: 14px;
    `;
    iosPrompt.innerHTML = `
      <div class="d-flex align-items-start">
        <i class="fas fa-info-circle me-2 mt-1"></i>
        <div class="flex-grow-1">
          <strong>نصب اپلیکیشن کلید</strong><br>
          <small>برای نصب: دکمه اشتراک‌گذاری <i class="fas fa-share"></i> را فشار دهید و "Add to Home Screen" را انتخاب کنید</small>
        </div>
        <button type="button" class="btn-close" onclick="hideIOSPrompt()"></button>
      </div>
    `;
    document.body.appendChild(iosPrompt);

    // Show with delay
    setTimeout(() => {
      iosPrompt.style.display = 'block';
    }, 3000);
  }
}

function hideIOSPrompt() {
  const prompt = document.getElementById('ios-install-prompt');
  if (prompt) {
    prompt.style.display = 'none';
  }
}

// Initialize iOS prompt
if (isIOS()) {
  window.addEventListener('load', () => {
    setTimeout(showIOSInstallInstructions, 2000);
  });
}
