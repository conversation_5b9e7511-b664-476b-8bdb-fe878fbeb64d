// PWA Installation and Service Worker Registration
// Use global variable to avoid conflicts
window.deferredPrompt = null;

// Register Service Worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// Device detection functions
function isDesktop() {
  return !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) &&
         window.innerWidth > 768;
}

function isAndroid() {
  return /Android/i.test(navigator.userAgent);
}

function isIOS() {
  return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
}

function isInWebView() {
  // Check if running inside APK WebView
  return window.navigator.userAgent.includes('wv') ||
         window.AndroidInterface !== undefined ||
         window.webkit?.messageHandlers !== undefined;
}

// Handle PWA install prompt
window.addEventListener('beforeinstallprompt', (e) => {
  // Prevent the mini-infobar from appearing on mobile
  e.preventDefault();
  // Stash the event so it can be triggered later
  window.deferredPrompt = e;
  // Show appropriate notification based on device
  showDeviceSpecificNotification();
});

// Show device-specific notification
function showDeviceSpecificNotification() {
  // Don't show any notification if in WebView (APK app)
  if (isInWebView()) {
    return;
  }

  // Don't show any notification on desktop
  if (isDesktop()) {
    return;
  }

  // Show Android notification
  if (isAndroid()) {
    showAndroidNotification();
  }

  // Show iOS notification
  if (isIOS()) {
    showIOSNotification();
  }
}

// Show Android install notification
function showAndroidNotification() {
  // Check if notification was already dismissed
  if (localStorage.getItem('android-install-dismissed') === 'true') {
    return;
  }

  // Create notification if it doesn't exist
  if (!document.getElementById('android-install-notification')) {
    const notification = document.createElement('div');
    notification.id = 'android-install-notification';
    notification.className = 'alert alert-success position-fixed';
    notification.style.cssText = `
      top: 20px;
      left: 20px;
      right: 20px;
      z-index: 1060;
      border-radius: 10px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
      animation: slideDown 0.3s ease;
    `;
    notification.innerHTML = `
      <div class="d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
          <i class="fab fa-android text-success me-2" style="font-size: 1.5rem;"></i>
          <div>
            <strong>نصب اپلیکیشن کلید</strong><br>
            <small>اپلیکیشن را روی گوشی خود نصب کنید</small>
          </div>
        </div>
        <div class="d-flex align-items-center">
          <a href="/Info/Download" class="btn btn-success btn-sm me-2">
            <i class="fas fa-download me-1"></i>نصب
          </a>
          <button type="button" class="btn-close" onclick="dismissAndroidNotification()"></button>
        </div>
      </div>
    `;
    document.body.appendChild(notification);

    // Auto hide after 10 seconds
    setTimeout(() => {
      if (document.getElementById('android-install-notification')) {
        dismissAndroidNotification();
      }
    }, 10000);
  }
}

// Show iOS install notification
function showIOSNotification() {
  // Check if notification was already dismissed
  if (localStorage.getItem('ios-install-dismissed') === 'true') {
    return;
  }

  // Create notification if it doesn't exist
  if (!document.getElementById('ios-install-notification')) {
    const notification = document.createElement('div');
    notification.id = 'ios-install-notification';
    notification.className = 'alert alert-info position-fixed';
    notification.style.cssText = `
      top: 20px;
      left: 20px;
      right: 20px;
      z-index: 1060;
      border-radius: 10px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
      animation: slideDown 0.3s ease;
    `;
    notification.innerHTML = `
      <div class="d-flex align-items-start justify-content-between">
        <div class="d-flex align-items-start">
          <i class="fab fa-apple text-primary me-2" style="font-size: 1.5rem;"></i>
          <div>
            <strong>نصب اپلیکیشن کلید</strong><br>
            <small>
              برای نصب: دکمه اشتراک‌گذاری <i class="fas fa-share"></i> را فشار دهید<br>
              سپس "Add to Home Screen" را انتخاب کنید
            </small>
          </div>
        </div>
        <button type="button" class="btn-close" onclick="dismissIOSNotification()"></button>
      </div>
    `;
    document.body.appendChild(notification);

    // Auto hide after 15 seconds
    setTimeout(() => {
      if (document.getElementById('ios-install-notification')) {
        dismissIOSNotification();
      }
    }, 15000);
  }
}

// Dismiss Android notification
function dismissAndroidNotification() {
  const notification = document.getElementById('android-install-notification');
  if (notification) {
    notification.style.animation = 'slideUp 0.3s ease';
    setTimeout(() => {
      notification.remove();
    }, 300);
    // Remember dismissal
    localStorage.setItem('android-install-dismissed', 'true');
  }
}

// Dismiss iOS notification
function dismissIOSNotification() {
  const notification = document.getElementById('ios-install-notification');
  if (notification) {
    notification.style.animation = 'slideUp 0.3s ease';
    setTimeout(() => {
      notification.remove();
    }, 300);
    // Remember dismissal
    localStorage.setItem('ios-install-dismissed', 'true');
  }
}

// Go to download page
function goToDownloadPage() {
  window.location.href = '/Info/Download';
}

// Install PWA (kept for download page usage)
async function installPWA() {
  if (!window.deferredPrompt) return;

  // Show the install prompt
  window.deferredPrompt.prompt();

  // Wait for the user to respond to the prompt
  const { outcome } = await window.deferredPrompt.userChoice;

  if (outcome === 'accepted') {
    console.log('User accepted the install prompt');
    hideInstallButton();
  } else {
    console.log('User dismissed the install prompt');
  }

  // Clear the deferredPrompt
  window.deferredPrompt = null;
}

// Hide install button (kept for compatibility)
function hideInstallButton() {
  // No longer needed but kept for compatibility
}

// Handle app installed event
window.addEventListener('appinstalled', () => {
  console.log('PWA was installed');
  hideInstallButton();
  
  // Show success message
  if (typeof bootstrap !== 'undefined') {
    const toast = document.createElement('div');
    toast.className = 'toast position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060;';
    toast.innerHTML = `
      <div class="toast-header">
        <i class="fas fa-check-circle text-success me-2"></i>
        <strong class="me-auto">کلید</strong>
        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
      </div>
      <div class="toast-body">
        اپلیکیشن با موفقیت نصب شد!
      </div>
    `;
    document.body.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
      document.body.removeChild(toast);
    });
  }
});

// Check if app is running in standalone mode
function isStandalone() {
  return window.matchMedia('(display-mode: standalone)').matches ||
         window.navigator.standalone === true;
}

// Make functions globally accessible
window.dismissAndroidNotification = dismissAndroidNotification;
window.dismissIOSNotification = dismissIOSNotification;

// Initialize notifications on page load
window.addEventListener('load', () => {
  // Don't show notifications if already in standalone mode
  if (isStandalone()) {
    return;
  }

  // Show notification with delay
  setTimeout(() => {
    showDeviceSpecificNotification();
  }, 2000);
});
