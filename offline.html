<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>آفلاین - کلید</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #a62626 0%, #d63384 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        
        .offline-container {
            max-width: 400px;
            padding: 2rem;
        }
        
        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        .offline-title {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            font-weight: bold;
        }
        
        .offline-message {
            font-size: 1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }
        
        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 1; }
            100% { opacity: 0.8; }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon pulse">📱</div>
        <h1 class="offline-title">اتصال اینترنت برقرار نیست</h1>
        <p class="offline-message">
            لطفاً اتصال اینترنت خود را بررسی کنید و دوباره تلاش کنید.
        </p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            🔄 تلاش مجدد
        </button>
    </div>
    
    <script>
        // Auto-reload when connection is restored
        function checkConnection() {
            if (navigator.onLine) {
                window.location.reload();
            }
        }
        
        window.addEventListener('online', checkConnection);
        setInterval(checkConnection, 5000);
    </script>
</body>
</html>
