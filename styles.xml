<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Base Application Theme -->
    <style name="KeleidAppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/keleid_red</item>
        <item name="colorPrimaryDark">@color/keleid_red_dark</item>
        <item name="colorAccent">@color/keleid_red</item>
        <item name="android:statusBarColor">@color/keleid_red_dark</item>
        <item name="android:navigationBarColor">@color/keleid_red_dark</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>

    <!-- Main Activity Theme (No Action Bar) -->
    <style name="KeleidAppTheme.NoActionBar" parent="KeleidAppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- Splash Screen Theme -->
    <style name="KeleidSplashTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@color/keleid_red_dark</item>
    </style>

    <!-- Progress Bar Style -->
    <style name="KeleidProgressBar" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:progressTint">@color/keleid_red</item>
        <item name="android:progressBackgroundTint">@color/progress_background</item>
        <item name="android:indeterminateTint">@color/keleid_red</item>
    </style>

</resources>
