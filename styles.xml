<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/keleid_red</item>
        <item name="colorPrimaryDark">@color/keleid_red_dark</item>
        <item name="colorAccent">@color/keleid_red</item>
        <item name="android:statusBarColor">@color/keleid_red_dark</item>
        <item name="android:navigationBarColor">@color/keleid_red_dark</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>

    <!-- No Action Bar theme for MainActivity -->
    <style name="AppTheme.NoActionBar" parent="AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- Splash Screen theme -->
    <style name="SplashTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@color/keleid_red_dark</item>
    </style>

    <!-- Progress Bar style -->
    <style name="ProgressBarStyle" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:progressTint">@color/keleid_red</item>
        <item name="android:progressBackgroundTint">@color/progress_background</item>
    </style>

</resources>
