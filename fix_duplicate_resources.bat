@echo off
echo ========================================
echo    Fixing Duplicate Resources
echo ========================================
echo.

set "VALUES_PATH=app\src\main\res\values"

echo Checking for duplicate resource files...
echo.

if exist "%VALUES_PATH%\styles.xml" (
    echo ✓ Found styles.xml
) else (
    echo ✗ styles.xml not found
)

if exist "%VALUES_PATH%\colors.xml" (
    echo ✓ Found colors.xml
) else (
    echo ✗ colors.xml not found
)

if exist "%VALUES_PATH%\strings.xml" (
    echo ✓ Found strings.xml
) else (
    echo ✗ strings.xml not found
)

echo.
echo Checking for conflicting theme files...

if exist "%VALUES_PATH%\themes.xml" (
    echo ! Found themes.xml - this might conflict with styles.xml
    echo   Recommendation: Use either styles.xml OR themes.xml, not both
) else (
    echo ✓ No conflicting themes.xml found
)

echo.
echo ========================================
echo         RESOURCE CHECK COMPLETE
echo ========================================
echo.
echo Solutions for duplicate resources:
echo.
echo 1. Make sure you have only ONE of these files:
echo    - styles.xml (recommended for this project)
echo    - themes.xml (alternative)
echo.
echo 2. If you have both, delete themes.xml and keep styles.xml
echo.
echo 3. Make sure resource names are unique:
echo    - Use "Keleid" prefix for custom styles
echo    - Example: KeleidAppTheme instead of AppTheme
echo.
echo 4. Clean and rebuild project:
echo    - Build → Clean Project
echo    - Build → Rebuild Project
echo.
pause
