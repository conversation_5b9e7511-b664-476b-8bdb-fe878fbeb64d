@echo off
echo ========================================
echo    Keleid Android Project Setup
echo ========================================
echo.

echo Step 1: Creating keystore for app signing...
echo Please fill in the following information:
echo.
call create_keystore.bat

echo.
echo Step 2: Please update gradle.properties with your keystore information:
echo - KELEID_STORE_PASSWORD=your_store_password
echo - KELEID_KEY_PASSWORD=your_key_password
echo.

echo Step 3: Update website URL in MainActivity.java:
echo - Change "https://keleid.ir" to your actual website URL
echo.

echo Step 4: Customize app details:
echo - Update app name in strings.xml
echo - Replace app icons in res/mipmap/ folders
echo - Update colors in colors.xml if needed
echo.

echo Step 5: Test the app:
echo - Run: gradlew assembleDebug
echo - Install on device and test
echo.

echo Step 6: Build release:
echo - Run: build_release.bat
echo - Upload AAB file to Google Play Console
echo.

echo ========================================
echo         SETUP INSTRUCTIONS COMPLETE
echo ========================================
echo.
pause
