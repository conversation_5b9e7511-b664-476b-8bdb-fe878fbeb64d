# راهنمای اضافه کردن لوگو به اپلیکیشن

## 📁 مسیرهای مورد نیاز

برای اضافه کردن لوگوی خود، فایل عکس را با نام `keleid_logo.png` در پوشه‌های زیر قرار دهید:

### ساختار پوشه‌ها:
```
app/src/main/res/
├── drawable/
│   └── keleid_logo.png          (برای تراکم عادی)
├── drawable-hdpi/
│   └── keleid_logo.png          (72 dpi - 108x108 px)
├── drawable-mdpi/
│   └── keleid_logo.png          (48 dpi - 72x72 px)
├── drawable-xhdpi/
│   └── keleid_logo.png          (96 dpi - 144x144 px)
├── drawable-xxhdpi/
│   └── keleid_logo.png          (144 dpi - 216x216 px)
└── drawable-xxxhdpi/
    └── keleid_logo.png          (192 dpi - 288x288 px)
```

## 🎨 سایزهای توصیه شده

| پوشه | تراکم | سایز پیکسل |
|------|--------|------------|
| drawable-mdpi | 160 dpi | 72x72 px |
| drawable-hdpi | 240 dpi | 108x108 px |
| drawable-xhdpi | 320 dpi | 144x144 px |
| drawable-xxhdpi | 480 dpi | 216x216 px |
| drawable-xxxhdpi | 640 dpi | 288x288 px |

## 🔧 مراحل اضافه کردن:

### روش 1: در Android Studio
1. کلیک راست روی `app/src/main/res/drawable`
2. انتخاب `New → Image Asset`
3. انتخاب `Asset Type: Image`
4. انتخاب فایل لوگوی خود
5. تغییر نام به `keleid_logo`
6. کلیک `Next` و `Finish`

### روش 2: دستی
1. فایل لوگو را با نام `keleid_logo.png` ذخیره کنید
2. آن را در پوشه `app/src/main/res/drawable/` کپی کنید
3. برای کیفیت بهتر، سایزهای مختلف را در پوشه‌های مربوطه قرار دهید

## 📝 نکات مهم:

- **فرمت فایل**: PNG با پس‌زمینه شفاف توصیه می‌شود
- **نام فایل**: فقط حروف کوچک، اعداد و underscore
- **سایز**: حداکثر 512x512 پیکسل برای drawable اصلی
- **کیفیت**: استفاده از vector یا PNG با کیفیت بالا

## 🎯 تست کردن:

پس از اضافه کردن لوگو:
1. پروژه را Clean کنید: `Build → Clean Project`
2. Rebuild کنید: `Build → Rebuild Project`
3. اپلیکیشن را اجرا کنید

## ⚠️ عیب‌یابی:

اگر لوگو نمایش داده نمی‌شود:
- نام فایل را بررسی کنید (keleid_logo.png)
- مسیر فایل را بررسی کنید (drawable folder)
- پروژه را Clean و Rebuild کنید
- فرمت فایل PNG باشد
