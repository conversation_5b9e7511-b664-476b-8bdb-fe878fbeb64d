# Project-wide Gradle settings.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.caching=true
android.useAndroidX=true
android.enableJetifier=true

# Signing Config (fill these after creating keystore)
KELEID_STORE_FILE=keleid-release-key.keystore
KELEID_STORE_PASSWORD=your_store_password_here
KELEID_KEY_ALIAS=keleid
KELEID_KEY_PASSWORD=your_key_password_here

# Performance optimizations
org.gradle.configureondemand=true
android.enableBuildCache=true
