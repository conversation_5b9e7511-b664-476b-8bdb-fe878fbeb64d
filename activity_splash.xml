<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/splash_background"
    android:gravity="center">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/splash_logo"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginBottom="24dp"
            android:src="@drawable/ic_keleid_logo"
            android:contentDescription="@string/app_name" />

        <TextView
            android:id="@+id/splash_app_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="@string/app_name"
            android:textColor="@android:color/white"
            android:textSize="32sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            android:letterSpacing="0.05"
            android:shadowColor="#80000000"
            android:shadowDx="2"
            android:shadowDy="2"
            android:shadowRadius="4" />

        <TextView
            android:id="@+id/splash_tagline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_tagline"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:alpha="0.9"
            android:fontFamily="sans-serif"
            android:letterSpacing="0.02"
            android:shadowColor="#80000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2" />

    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="32dp"
        android:text="نسخه 1.0.0"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        android:alpha="0.7" />

</RelativeLayout>
