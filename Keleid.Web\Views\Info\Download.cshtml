@{
    ViewData["Title"] = "دانلود اپلیکیشن";
}

<!-- Header -->
@await Html.PartialAsync("_SecHeader", "دانلود اپلیکیشن")

<div class="container my-5 download-page">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header -->
            <div class="text-center mb-5">
                <img src="/assets/img/logo.png" alt="کلید" height="60" class="mb-3">
                <h1 class="h2 mb-3" style="color: #a62626;">دانلود اپلیکیشن کلید</h1>
                <p class="text-muted">اپلیکیشن کلید را روی گوشی خود نصب کنید و از تجربه بهتری لذت ببرید</p>
            </div>

            <div class="row">
                <!-- PWA Installation -->
                <div class="col-lg-6 mb-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body p-4 text-center">
                            <div class="mb-4">
                                <i class="fas fa-mobile-alt fa-3x" style="color: #a62626;"></i>
                            </div>
                            <h4 class="h5 mb-3">نصب مستقیم (PWA)</h4>
                            <p class="text-muted mb-4">
                                اپلیکیشن را مستقیماً از مرورگر خود نصب کنید. این روش برای تمام دستگاه‌ها قابل استفاده است.
                            </p>

                            <!-- Mobile Install Button -->
                            <button id="pwa-install-main-btn" class="btn btn-danger btn-lg mb-3 d-block d-md-none" style="display: none;">
                                <i class="fas fa-mobile-alt me-2"></i>
                                نصب اپلیکیشن
                            </button>

                            <!-- Instructions for different browsers -->
                            <div class="installation-instructions">
                                <!-- Chrome/Edge Instructions -->
                                <div class="instruction-item mb-3" id="chrome-instructions">
                                    <h6 class="text-dark">مرورگر Chrome/Edge:</h6>
                                    <small class="text-muted">
                                        روی آیکون <i class="fas fa-download"></i> در نوار آدرس کلیک کنید یا از منو "نصب کلید" را انتخاب کنید.
                                    </small>
                                </div>

                                <!-- Safari iOS Instructions -->
                                <div class="instruction-item mb-3" id="ios-instructions" style="display: none;">
                                    <h6 class="text-dark">Safari (iOS):</h6>
                                    <small class="text-muted">
                                        1. روی آیکون اشتراک‌گذاری <i class="fas fa-share"></i> کلیک کنید<br>
                                        2. "Add to Home Screen" را انتخاب کنید<br>
                                        3. "Add" را بزنید
                                    </small>
                                </div>

                                <!-- Safari Desktop Instructions -->
                                <div class="instruction-item mb-3" id="safari-instructions" style="display: none;">
                                    <h6 class="text-dark">Safari (دسکتاپ):</h6>
                                    <small class="text-muted">
                                        Safari فعلاً از نصب PWA پشتیبانی نمی‌کند. لطفاً از Chrome یا Firefox استفاده کنید.
                                    </small>
                                </div>

                                <!-- Firefox Instructions -->
                                <div class="instruction-item mb-3" id="firefox-instructions" style="display: none;">
                                    <h6 class="text-dark">مرورگر Firefox:</h6>
                                    <small class="text-muted">
                                        از منو مرورگر "Install" یا "نصب" را انتخاب کنید.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Android Download -->
                <div class="col-lg-6 mb-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body p-4 text-center">
                            <div class="mb-4">
                                <i class="fab fa-android fa-3x" style="color: #4CAF50;"></i>
                            </div>
                            <h4 class="h5 mb-3">دانلود برای اندروید</h4>
                            <p class="text-muted mb-4">
                                اپلیکیشن اندروید کلید را از طریق لینک‌های زیر دانلود کنید.
                            </p>
                            
                            <!-- Direct APK Download -->
                            <a href="#" class="btn btn-success btn-lg mb-3 w-100" onclick="downloadAPK()">
                                <i class="fas fa-download me-2"></i>
                                دانلود مستقیم APK
                            </a>
                            
                            <!-- Google Play Store -->
                            <a href="#" class="btn btn-outline-success btn-lg mb-3 w-100" onclick="openGooglePlay()">
                                <i class="fab fa-google-play me-2"></i>
                                دانلود از گوگل پلی
                            </a>
                            
                            <!-- Bazaar -->
                            <a href="#" class="btn btn-outline-primary btn-lg w-100" onclick="openBazaar()">
                                <i class="fas fa-store me-2"></i>
                                دانلود از بازار
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQ Section -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h4 class="h5 mb-4 text-center" style="color: #a62626;">
                                <i class="fas fa-question-circle me-2"></i>
                                سوالات متداول
                            </h4>
                            
                            <div class="accordion" id="downloadFAQ">
                                <div class="accordion-item border-0 mb-2">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                            تفاوت PWA با اپلیکیشن معمولی چیست؟
                                        </button>
                                    </h2>
                                    <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#downloadFAQ">
                                        <div class="accordion-body">
                                            PWA (Progressive Web App) نسخه وب اپلیکیشن است که مانند اپلیکیشن معمولی کار می‌کند. مزایای آن شامل نصب آسان، حجم کم و به‌روزرسانی خودکار است.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item border-0 mb-2">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                            آیا اپلیکیشن رایگان است؟
                                        </button>
                                    </h2>
                                    <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#downloadFAQ">
                                        <div class="accordion-body">
                                            بله، اپلیکیشن کلید کاملاً رایگان است و هیچ هزینه‌ای برای دانلود یا استفاده از آن دریافت نمی‌شود.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item border-0">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                            چگونه اپلیکیشن را به‌روزرسانی کنم؟
                                        </button>
                                    </h2>
                                    <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#downloadFAQ">
                                        <div class="accordion-body">
                                            اپلیکیشن PWA به‌طور خودکار به‌روزرسانی می‌شود. برای اپلیکیشن اندروید، از طریق فروشگاهی که دانلود کرده‌اید، به‌روزرسانی کنید.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section js {
<script>
// PWA Install functionality for download page
// Use global deferredPrompt from pwa.js instead of declaring new one

// Check if PWA install is available
window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    // Use global deferredPrompt from pwa.js
    window.deferredPrompt = e;

    // Show desktop install button
    const desktopBtn = document.getElementById('desktop-install-btn');
    if (desktopBtn) {
        desktopBtn.style.display = 'block';
    }

    // Show main install button for mobile
    const mainBtn = document.getElementById('pwa-install-main-btn');
    if (mainBtn && isMobileDevice()) {
        mainBtn.style.display = 'block';
    }
});

// Install PWA from download page (mobile)
document.getElementById('pwa-install-main-btn')?.addEventListener('click', async () => {
    if (!window.deferredPrompt) return;

    window.deferredPrompt.prompt();
    const { outcome } = await window.deferredPrompt.userChoice;

    if (outcome === 'accepted') {
        document.getElementById('pwa-install-main-btn').style.display = 'none';
        showSuccessMessage('اپلیکیشن با موفقیت نصب شد!');
    }

    window.deferredPrompt = null;
});

// Desktop install button
document.getElementById('desktop-install-btn')?.addEventListener('click', async () => {
    if (!window.deferredPrompt) return;

    window.deferredPrompt.prompt();
    const { outcome } = await window.deferredPrompt.userChoice;

    if (outcome === 'accepted') {
        document.getElementById('desktop-install-btn').style.display = 'none';
        showSuccessMessage('اپلیکیشن با موفقیت روی کامپیوتر نصب شد!');
    }

    window.deferredPrompt = null;
});

// Check if device is mobile (local function for this page)
function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
           window.innerWidth <= 768;
}

// Show success message
function showSuccessMessage(message) {
    if (typeof bootstrap !== 'undefined') {
        const toast = document.createElement('div');
        toast.className = 'toast position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060;';
        toast.innerHTML = `
            <div class="toast-header">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong class="me-auto">کلید</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;
        document.body.appendChild(toast);

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        toast.addEventListener('hidden.bs.toast', () => {
            document.body.removeChild(toast);
        });
    } else {
        alert(message);
    }
}

// Detect browser and show appropriate instructions
function detectBrowser() {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    const isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);
    const isEdge = /Edg/.test(navigator.userAgent);

    // Hide all instructions first
    const instructions = ['chrome-instructions', 'ios-instructions', 'firefox-instructions', 'safari-instructions'];
    instructions.forEach(id => {
        const element = document.getElementById(id);
        if (element) element.style.display = 'none';
    });

    // Show appropriate instruction
    let targetInstruction = 'chrome-instructions'; // default

    if (isIOS) {
        targetInstruction = 'ios-instructions';
    } else if (isFirefox) {
        targetInstruction = 'firefox-instructions';
    } else if (isSafari && !isIOS) {
        targetInstruction = 'safari-instructions';
    } else if (isChrome || isEdge) {
        targetInstruction = 'chrome-instructions';
    }

    const targetElement = document.getElementById(targetInstruction);
    if (targetElement) {
        targetElement.style.display = 'block';
    }
}

// Download functions
function downloadAPK() {
    alert('لینک دانلود APK به زودی فعال خواهد شد.');
}

function openGooglePlay() {
    alert('لینک گوگل پلی به زودی فعال خواهد شد.');
}

function openBazaar() {
    alert('لینک بازار به زودی فعال خواهد شد.');
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    detectBrowser();
});
</script>
}
