# Keleid Android App

اپلیکیشن اندروید کلید - نیازمندی‌های رایگان صنعتی

## 🚀 راه‌اندازی سریع

### 1. پیش‌نیازها
- Android Studio Arctic Fox یا جدیدتر
- JDK 8 یا جدیدتر
- Android SDK API 21+ (Android 5.0+)

### 2. راه‌اندازی پروژه
```bash
# 1. اجرای اسکریپت راه‌اندازی
setup_project.bat

# 2. ایجاد keystore
create_keystore.bat

# 3. به‌روزرسانی gradle.properties با اطلاعات keystore

# 4. تغییر URL سایت در MainActivity.java
```

### 3. تست و توسعه
```bash
# Debug build
gradlew assembleDebug

# نصب روی دستگاه
adb install app/build/outputs/apk/debug/app-debug.apk
```

### 4. ساخت Release
```bash
# اجرای اسکریپت build
build_release.bat

# یا دستی:
gradlew clean
gradlew assembleRelease
gradlew bundleRelease
```

## 📁 ساختار فایل‌ها

```
app/
├── src/main/
│   ├── java/com/keleid/app/
│   │   ├── MainActivity.java      # اکتیویتی اصلی
│   │   └── SplashActivity.java    # صفحه آغازین
│   ├── res/
│   │   ├── layout/
│   │   │   ├── activity_main.xml
│   │   │   └── activity_splash.xml
│   │   ├── values/
│   │   │   ├── colors.xml
│   │   │   ├── strings.xml
│   │   │   └── styles.xml
│   │   ├── anim/                  # انیمیشن‌ها
│   │   └── mipmap/                # آیکون‌ها
│   ├── assets/
│   │   └── offline.html           # صفحه آفلاین
│   └── AndroidManifest.xml
├── build.gradle                   # تنظیمات build
└── proguard-rules.pro            # قوانین بهینه‌سازی
```

## ⚙️ تنظیمات مهم

### تغییر URL سایت
در `MainActivity.java`:
```java
private final String websiteUrl = "https://your-website.com";
```

### تنظیم Signing
در `gradle.properties`:
```properties
KELEID_STORE_FILE=keleid-release-key.keystore
KELEID_STORE_PASSWORD=your_password
KELEID_KEY_ALIAS=keleid
KELEID_KEY_PASSWORD=your_password
```

### شخصی‌سازی
- **نام اپ**: `res/values/strings.xml`
- **رنگ‌ها**: `res/values/colors.xml`
- **آیکون**: `res/mipmap/` folders
- **Splash Screen**: `res/layout/activity_splash.xml`

## 🔧 ویژگی‌های پیاده‌شده

✅ **WebView بهینه شده** با تنظیمات performance  
✅ **Splash Screen** با انیمیشن  
✅ **حذف خودکار PWA notifications**  
✅ **صفحه آفلاین** برای زمان قطع اینترنت  
✅ **Progress Bar** برای نمایش وضعیت بارگذاری  
✅ **Back Button** handling  
✅ **ProGuard** برای بهینه‌سازی release  
✅ **Signing Config** برای انتشار  

## 📦 خروجی‌های Build

پس از اجرای `build_release.bat`:

- **Debug APK**: `app/build/outputs/apk/debug/app-debug.apk`
- **Release APK**: `app/build/outputs/apk/release/app-release.apk`
- **AAB Bundle**: `app/build/outputs/bundle/release/app-release.aab`

## 🚀 انتشار در Google Play

1. فایل `app-release.aab` را آپلود کنید
2. اطلاعات اپلیکیشن را تکمیل کنید
3. اسکرین‌شات‌ها و توضیحات را اضافه کنید
4. برای بررسی ارسال کنید

## 🐛 عیب‌یابی

### مشکلات رایج:
- **Keystore not found**: مسیر keystore در `gradle.properties` را بررسی کنید
- **Build failed**: `gradlew clean` را اجرا کنید
- **WebView blank**: URL سایت و دسترسی اینترنت را بررسی کنید

### لاگ‌ها:
```bash
# مشاهده لاگ‌های دستگاه
adb logcat | grep Keleid
```

## 📞 پشتیبانی

برای مشکلات فنی یا سوالات، با تیم توسعه تماس بگیرید.
