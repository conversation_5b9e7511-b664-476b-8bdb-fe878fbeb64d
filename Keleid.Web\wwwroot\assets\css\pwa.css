/* PWA Specific Styles */

/* Install Button Animation */
#pwa-install-btn {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

#pwa-install-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(166, 38, 38, 0.3) !important;
}

/* iOS Install Prompt */
#ios-install-prompt {
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Download Page Specific Styles */
.download-page .card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.download-page .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.installation-instructions {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.instruction-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.instruction-item:last-child {
    border-bottom: none;
}

/* Feature Icons Animation */
.feature-icon {
    transition: transform 0.2s ease;
}

.feature-icon:hover {
    transform: scale(1.1);
}

/* Hide floating install button on desktop */
@media (min-width: 769px) {
    #pwa-install-btn,
    #ios-install-prompt {
        display: none !important;
    }
}

/* Responsive Design for PWA */
@media (max-width: 768px) {
    #pwa-install-btn {
        left: 10px;
        right: 10px;
        bottom: 90px;
        width: auto;
    }

    #ios-install-prompt {
        left: 10px;
        right: 10px;
        bottom: 90px;
    }

    .download-page .btn-lg {
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }
}

/* PWA Standalone Mode Styles */
@media (display-mode: standalone) {
    /* Hide install buttons when in standalone mode */
    #pwa-install-btn,
    #pwa-install-main-btn,
    #ios-install-prompt {
        display: none !important;
    }
    
    /* Add some top padding to account for status bar */
    body {
        padding-top: env(safe-area-inset-top);
    }
    
    /* Adjust navbar for standalone mode */
    .navbar {
        padding-top: calc(0.5rem + env(safe-area-inset-top));
    }
}

/* Dark mode support for PWA */
@media (prefers-color-scheme: dark) {
    .installation-instructions {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .instruction-item {
        border-bottom-color: #4a5568;
    }
}

/* Loading animation for PWA */
.pwa-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #a62626;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success animation */
.pwa-success {
    color: #28a745;
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Download buttons hover effects */
.btn-download {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-download:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-download:hover:before {
    left: 100%;
}

/* Accordion custom styles for FAQ */
.accordion-button:not(.collapsed) {
    background-color: #a62626;
    color: white;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(166, 38, 38, 0.25);
}

/* Custom scrollbar for PWA */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #a62626;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #8b1e1e;
}
