@echo off
echo ========================================
echo    Building Keleid Android App
echo ========================================
echo.

echo Step 1: Cleaning previous builds...
call gradlew clean
if %errorlevel% neq 0 (
    echo ERROR: Clean failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Building debug APK...
call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo ERROR: Debug build failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Building release APK...
call gradlew assembleRelease
if %errorlevel% neq 0 (
    echo ERROR: Release build failed!
    pause
    exit /b 1
)

echo.
echo Step 4: Building AAB for Google Play...
call gradlew bundleRelease
if %errorlevel% neq 0 (
    echo ERROR: AAB build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo           BUILD SUCCESSFUL!
echo ========================================
echo.
echo Files created:
echo - Debug APK: app\build\outputs\apk\debug\app-debug.apk
echo - Release APK: app\build\outputs\apk\release\app-release.apk
echo - Release AAB: app\build\outputs\bundle\release\app-release.aab
echo.
echo The AAB file is ready for Google Play Store upload!
echo.
pause
