@echo off
echo ========================================
echo    Keleid Logo Setup Script
echo ========================================
echo.

set "DRAWABLE_PATH=app\src\main\res\drawable"
set "LOGO_FILE=keleid_logo.png"

echo Please place your logo file named "%LOGO_FILE%" in the current directory.
echo.
echo Current directory: %CD%
echo.

if not exist "%LOGO_FILE%" (
    echo ERROR: %LOGO_FILE% not found in current directory!
    echo.
    echo Please:
    echo 1. Place your logo file in this directory
    echo 2. Rename it to "%LOGO_FILE%"
    echo 3. Run this script again
    echo.
    pause
    exit /b 1
)

echo Found %LOGO_FILE%
echo.

if not exist "%DRAWABLE_PATH%" (
    echo Creating drawable directory...
    mkdir "%DRAWABLE_PATH%"
)

echo Copying logo to drawable folder...
copy "%LOGO_FILE%" "%DRAWABLE_PATH%\%LOGO_FILE%"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo         LOGO SETUP SUCCESSFUL!
    echo ========================================
    echo.
    echo Logo has been copied to: %DRAWABLE_PATH%\%LOGO_FILE%
    echo.
    echo Next steps:
    echo 1. Open Android Studio
    echo 2. Clean and Rebuild project
    echo 3. Run the app to see your logo
    echo.
) else (
    echo.
    echo ERROR: Failed to copy logo file!
    echo Please check file permissions and try again.
    echo.
)

pause
