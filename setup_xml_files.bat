@echo off
echo ========================================
echo    Setting up XML files for Android
echo ========================================
echo.

set "XML_PATH=app\src\main\res\xml"

echo Creating XML directory...
if not exist "%XML_PATH%" (
    mkdir "%XML_PATH%"
    echo Created: %XML_PATH%
) else (
    echo Directory already exists: %XML_PATH%
)

echo.
echo Copying XML files...

if exist "res_xml_file_paths.xml" (
    copy "res_xml_file_paths.xml" "%XML_PATH%\file_paths.xml"
    echo ✓ Copied file_paths.xml
) else (
    echo ✗ res_xml_file_paths.xml not found
)

if exist "res_xml_backup_rules.xml" (
    copy "res_xml_backup_rules.xml" "%XML_PATH%\backup_rules.xml"
    echo ✓ Copied backup_rules.xml
) else (
    echo ✗ res_xml_backup_rules.xml not found
)

if exist "res_xml_data_extraction_rules.xml" (
    copy "res_xml_data_extraction_rules.xml" "%XML_PATH%\data_extraction_rules.xml"
    echo ✓ Copied data_extraction_rules.xml
) else (
    echo ✗ res_xml_data_extraction_rules.xml not found
)

echo.
echo ========================================
echo         XML FILES SETUP COMPLETE
echo ========================================
echo.
echo Files created in: %XML_PATH%
echo - file_paths.xml
echo - backup_rules.xml  
echo - data_extraction_rules.xml
echo.
echo Next steps:
echo 1. Open Android Studio
echo 2. Clean Project (Build → Clean Project)
echo 3. Rebuild Project (Build → Rebuild Project)
echo.
pause
