plugins {
    id 'com.android.application'
}

android {
    namespace 'identifier.keleid.app'
    compileSdk 34

    defaultConfig {
        applicationId "identifier.keleid.app"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0.0"
        
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        // Performance optimizations
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
    }

    signingConfigs {
        release {
            if (project.hasProperty('KELEID_STORE_FILE')) {
                storeFile file(KELEID_STORE_FILE)
                storePassword KELEID_STORE_PASSWORD
                keyAlias KELEID_KEY_ALIAS
                keyPassword KELEID_KEY_PASSWORD
            }
        }
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            shrinkResources false
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }
        
        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            
            // Performance optimizations
            zipAlignEnabled true
            crunchPngs true
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        viewBinding true
    }

    packagingOptions {
        resources {
            excludes += ['META-INF/DEPENDENCIES', 'META-INF/LICENSE', 'META-INF/LICENSE.txt', 'META-INF/NOTICE', 'META-INF/NOTICE.txt']
        }
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
